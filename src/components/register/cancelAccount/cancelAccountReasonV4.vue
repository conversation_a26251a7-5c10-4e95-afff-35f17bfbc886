<template>
  <fragment>
    <div class="com_title">
      <h5>
        感恩与您携手的美好时光，衷心希望您能告诉我们销户辞别的原因，让未来更好的国金与您再相逢。
      </h5>
    </div>

    <div class="cm_sele_wrap">
      <h5 class="title">请选择您转销户的原因(支持多选)∶</h5>

      <ul class="cm_sele_list reason-list">
        <fragment
          v-for="(parentReason, parentIndex) in cancelReasonList"
          :key="parentIndex"
        >
          <li @click="handleReasonSelection(parentReason)">
            <div class="layout">
              <span
                class="icon_check"
                :class="{ checked: parentReason.isChecked }"
              ></span>
              <p>{{ parentReason.displayText }}</p>
            </div>
          </li>

          <template v-if="parentReason.isChecked">
            <li
              v-for="(childReason, childIndex) in parentReason.children"
              :key="`${parentIndex}-${childIndex}`"
              class="sub_item sub-reason-item"
              @click="handleReasonSelection(childReason)"
            >
              <div class="layout">
                <span
                  class="icon_check"
                  :class="{ checked: childReason.isChecked }"
                ></span>
                <p>{{ childReason.displayText }}</p>
              </div>
            </li>
          </template>
        </fragment>
      </ul>

      <div class="notes_input" v-if="shouldShowOtherInput">
        <textarea
          placeholder="请输入"
          maxlength="100"
          v-model="otherReasonText"
        ></textarea>
      </div>
    </div>
  </fragment>
</template>

<script>
import { queryDictProps } from '@/common/util';
import { EVENT_NAME } from '@/common/formEnum';
import { trackBusinessPageClick } from '@/common/tracking/businessTracking';
import pageIdentifierBus from '@/common/tracking/pageIdentifierBus';

export default {
  name: 'CancelAccountReasonV4',
  inject: ['eventMessage'],
  data() {
    return {
      cancelReasonList: [],
      otherReasonText: '',
      reasonMap: new Map(),
      otherReasonItem: null
    };
  },
  watch: {
    otherReasonText: {
      handler(newValue) {
        const emojiRegex =
          /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[A9|AE]\u3030|\uA9|\uAE|\u3030/gi;

        if (emojiRegex.test(newValue)) {
          this.otherReasonText = newValue.replace(emojiRegex, '');
        }
      }
    },

    isNextButtonEnabled: {
      handler(isEnabled) {
        if (isEnabled) {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 2,
            data: this.submit
          });
        } else {
          this.eventMessage(this, EVENT_NAME.NEXT_BTN, {
            btnStatus: 0
          });
        }
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    isNextButtonEnabled() {
      if (!this.hasSelectedReason) {
        return false;
      }
      return !this.shouldShowOtherInput || this.otherReasonText.trim() !== '';
    },

    hasSelectedReason() {
      for (const parentReason of this.cancelReasonList) {
        if (parentReason.isChecked) {
          if (parentReason.children?.length > 0) {
            if (parentReason.children.some((child) => child.isChecked)) {
              return true;
            }
          } else {
            return true;
          }
        }
      }
      return false;
    },

    selectedReasonsText() {
      const selectedReasons = [];

      for (const parentReason of this.cancelReasonList) {
        if (parentReason.isChecked) {
          if (parentReason.children?.length > 0) {
            for (const childReason of parentReason.children) {
              if (childReason.isChecked) {
                selectedReasons.push(
                  `${parentReason.displayText},${childReason.displayText}`
                );
              }
            }
          } else {
            selectedReasons.push(parentReason.displayText);
          }
        }
      }

      // 去重处理：使用 Set 去除重复项
      const uniqueReasons = [...new Set(selectedReasons)];
      return uniqueReasons.join(';');
    },

    shouldShowOtherInput() {
      return this.otherReasonItem?.isChecked || false;
    }
  },
  created() {
    this.$store.commit('flow/setWhiteBg', true);
  },

  mounted() {
    // 设置页面标识供容器埋点使用
    pageIdentifierBus.setPageIdentifier('xzxhyy');
    this.loadCancelReasons();
  },
  methods: {
    async loadCancelReasons() {
      try {
        const reasonData = await queryDictProps('bc.common.closeAccReason');
        this.cancelReasonList = this.buildReasonHierarchy(reasonData);
      } catch (error) {
        this.$TAlert({
          tips: error
        });
      }
    },

    buildReasonHierarchy(reasonList) {
      const parentReasons = [];
      const childReasons = [];

      for (const reason of reasonList) {
        const processedReason = this.processReasonItem(reason);

        if (reason.dictValue.length === 2) {
          const parentReason = {
            ...processedReason,
            children: []
          };
          // 使用 Vue.set 确保响应式
          this.$set(parentReason, 'isChecked', false);
          parentReasons.push(parentReason);
        } else if (reason.dictValue.length === 4) {
          const childReason = { ...processedReason };
          // 使用 Vue.set 确保响应式
          this.$set(childReason, 'isChecked', false);
          childReasons.push(childReason);
        }
      }

      const parentReasonMap = new Map();
      for (const parentReason of parentReasons) {
        parentReasonMap.set(parentReason.dictValue, parentReason);

        if (parentReason.dictValue === '99') {
          this.otherReasonItem = parentReason;
        }
      }

      for (const childReason of childReasons) {
        const parentValue = childReason.dictValue.substring(0, 2);
        const parentReason = parentReasonMap.get(parentValue);

        if (parentReason) {
          parentReason.children.push(childReason);
        }
      }

      this.buildReasonMap(parentReasons);
      return parentReasons;
    },

    // 分离展示文案和弹窗文案
    processReasonItem(reason) {
      const [displayText, warningText] = reason.dictLabel.split('|');

      return {
        ...reason,
        displayText: displayText.trim(),
        warningText: warningText?.trim() || null
      };
    },

    buildReasonMap(parentReasons) {
      this.reasonMap.clear();

      for (const parentReason of parentReasons) {
        this.reasonMap.set(parentReason.dictValue, parentReason);

        for (const childReason of parentReason.children) {
          this.reasonMap.set(childReason.dictValue, childReason);
        }
      }
    },
    handleReasonSelection(reasonItem) {
      // 取消勾选操作，直接执行，无需弹窗
      if (reasonItem.isChecked) {
        this.toggleReasonSelection(reasonItem);
        return;
      }

      // 勾选操作且有警告信息，则弹窗确认
      if (reasonItem.warningText) {
        this.showWarningDialog(reasonItem.warningText, reasonItem);
      } else {
        this.toggleReasonSelection(reasonItem);
      }
    },

    showWarningDialog(warningMessage, reasonItem) {
      this.$TAlert({
        title: '温馨提示',
        tips: warningMessage,
        hasCancel: true,
        confirmBtn: '取消销户',
        cancelBtn: '继续销户',
        confirm: () => {
          this.eventMessage(this, EVENT_NAME.TO_INDEX);
        },
        cancel: () => this.toggleReasonSelection(reasonItem)
      });
    },

    toggleReasonSelection(reasonItem) {
      this.$set(reasonItem, 'isChecked', !reasonItem.isChecked);

      if (this.isParentReason(reasonItem)) {
        this.handleParentReasonToggle(reasonItem);
      } else if (this.isChildReason(reasonItem)) {
        this.handleChildReasonToggle(reasonItem);
      }

      trackBusinessPageClick({
        bizType: $h.getSession('bizType'),
        pageIdentifier: 'xzxhyy',
        elementName: 'gxan',
        remarks: this.selectedReasonsText
      });

      // 调试：输出当前选中的原因
      console.log('当前选中的原因:', this.selectedReasonsText);
    },

    isParentReason(reasonItem) {
      return reasonItem.children && reasonItem.children.length > 0;
    },

    isChildReason(reasonItem) {
      return reasonItem.dictValue.length === 4;
    },

    handleParentReasonToggle(parentReason) {
      if (parentReason.children?.length > 0) {
        // 父级原因被取消选中时，所有子项取消全选
        if (!parentReason.isChecked) {
          parentReason.children.forEach((childReason) => {
            this.$set(childReason, 'isChecked', false);
          });
        }
      }
    },

    handleChildReasonToggle(childReason) {
      const parentValue = childReason.dictValue.substring(0, 2);
      const parentReason = this.reasonMap.get(parentValue);

      if (parentReason) {
        const hasSelectedChild = parentReason.children.some(
          (child) => child.isChecked
        );
        this.$set(parentReason, 'isChecked', hasSelectedChild);
      }
    },

    submit() {
      trackBusinessPageClick({
        bizType: $h.getSession('bizType'),
        pageIdentifier: 'xzxhyy',
        elementName: 'next'
      });
      this.eventMessage(this, EVENT_NAME.NEXT_STEP, {
        reason_acc_cancel: this.selectedReasonsText,
        reason_acc_cancel_other: this.shouldShowOtherInput
          ? this.otherReasonText
          : ''
      });
    }
  }
};
</script>
<style scoped>
.reason-list li:not(.sub-reason-item) {
  padding: 0.15rem 0;
}

.sub-reason-item {
  padding: 0.1rem 0 0.1rem 0.2rem;
}

h5.title > .page_num {
  width: 0.35rem;
  height: 0.22rem;
  display: inline-flex;
  background-color: #fa443a;
  color: #ffffff;
  font-size: 0.14rem;
  padding-left: 0.05rem;
  margin-right: 0.1rem;
}
</style>
