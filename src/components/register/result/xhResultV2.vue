<template>
  <section class="main fixed white_bg" style="position: fixed">
    <t-header />
    <article class="content">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon ing" />
          <h5><span v-if="showDetail">成功提交，</span>处理中</h5>
          <p v-if="showDetail">
            {{ resultDesc }}
          </p>
        </div>
        <div class="imp_c_tips no_icon">
          <p class="warn">
            <span class="imp"
              >温馨提示：若您的账户存在资金、持仓或其他未完结业务而不满足销户条件，将会影响办理进度。我司将在您了结相关业务后自动为您办理，请耐心等待。如有疑问可拨打客服95310进行咨询。</span
            >
          </p>
        </div>
        <div
          v-show="unifiedAccountList.length > 0"
          class="result_status_item"
          @click="unifiedAccOn = !unifiedAccOn"
        >
          <div class="h_tit">
            <h5>一码通账户</h5>
            <i class="drop_arrow" :class="{ on: unifiedAccOn }"></i>
          </div>
          <div class="result_info" v-show="unifiedAccOn">
            <ul>
              <li v-for="(item, i) in unifiedAccountList" :key="i">
                <span class="tit"
                  >{{ item.name }}：{{ item.account }}（{{
                    commentStatus(item)
                  }}）</span
                >
                <p>
                  <span class="state">{{ item.statusDesc }}</span>
                </p>
              </li>
            </ul>
          </div>
        </div>
        <div
          v-show="fundAccountList.length > 0"
          class="result_status_item"
          @click="fundAccOn = !fundAccOn"
        >
          <div class="h_tit">
            <h5>资金账户</h5>
            <i class="drop_arrow" :class="{ on: fundAccOn }"></i>
          </div>
          <div class="result_info" v-show="fundAccOn">
            <ul>
              <li v-for="(item, i) in fundAccountList" :key="i">
                <span class="tit" v-if="item.bankAccountName"
                  >{{ item.bankAccountName }}（{{ item.bankName }}）</span
                >
                <span class="tit" v-else
                  >{{ item.name }}：{{ item.account }}（{{
                    commentStatus(item)
                  }}）</span
                >
                <p>
                  <span class="state">{{ item.statusDesc }}</span>
                </p>
              </li>
            </ul>
          </div>
        </div>
        <div
          v-show="stockholderList.length > 0"
          class="result_status_item"
          @click="stockholderOn = !stockholderOn"
        >
          <div class="h_tit">
            <h5>证券账户</h5>
            <i class="drop_arrow" :class="{ on: stockholderOn }"></i>
          </div>
          <div class="result_info" v-show="stockholderOn">
            <ul>
              <li v-for="(item, i) in stockholderList" :key="i">
                <span class="tit"
                  >{{ item.name }}：{{ item.account }}（{{
                    commentStatus(item)
                  }}）</span
                >
                <p>
                  <span class="state">{{ item.statusDesc }}</span>
                </p>
              </li>
            </ul>
          </div>
        </div>
        <div
          v-show="financialAccList.length > 0"
          class="result_status_item"
          @click="financialAccOn = !financialAccOn"
        >
          <div class="h_tit">
            <h5>理财账户</h5>
            <i class="drop_arrow" :class="{ on: financialAccOn }"></i>
          </div>
          <div class="result_info" v-show="financialAccOn">
            <ul>
              <li v-for="(item, i) in financialAccList" :key="i">
                <span class="tit"
                  >{{ item.name }}：{{ item.account }}（{{
                    commentStatus(item)
                  }}）</span
                >
                <p>
                  <span class="state">{{ item.statusDesc }}</span>
                </p>
              </li>
            </ul>
          </div>
        </div>
        <div
          v-show="creditAccList.length > 0"
          class="result_status_item"
          @click="creditAccOn = !creditAccOn"
        >
          <div class="h_tit">
            <h5>信用账户</h5>
            <i class="drop_arrow" :class="{ on: creditAccOn }"></i>
          </div>
          <div class="result_info" v-show="creditAccOn">
            <ul>
              <li v-for="(item, i) in creditAccList" :key="i">
                <span class="tit"
                  >{{ item.name }}：{{ item.account }}（{{
                    commentStatus(item)
                  }}）</span
                >
                <p>
                  <span class="state">{{ item.statusDesc }}</span>
                </p>
              </li>
            </ul>
          </div>
        </div>
        <div
          v-show="optionAccList.length > 0"
          class="result_status_item"
          @click="optionAccOn = !optionAccOn"
        >
          <div class="h_tit">
            <h5>期权账户</h5>
            <i class="drop_arrow" :class="{ on: optionAccOn }"></i>
          </div>
          <div class="result_info" v-show="optionAccOn">
            <ul>
              <li v-for="(item, i) in optionAccList" :key="i">
                <span class="tit"
                  >{{ item.name }}：{{ item.account }}（{{
                    commentStatus(item)
                  }}）</span
                >
                <p>
                  <span class="state">{{ item.statusDesc }}</span>
                </p>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div v-if="tips" class="result_bottom_tips" v-html="tips" />
    </article>
    <footer class="footer white_bg">
      <div class="ce_btn black">
        <!-- <a v-if="showVisit" class="p_button" @click="toQuestion"
          >前往完成问卷</a
        > -->
        <a class="p_button border" @click="back">返回</a>
      </div>
    </footer>
  </section>
</template>

<script>
import { BANDLE_STATE, TASK_TYPE, TASK_STATUS } from '@/common/enumeration';
import { EVENT_NAME } from '@/common/formEnum';
import {
  flowSubmit,
  flowQueryIns,
  addClientCritMark,
  xhAccountQuery,
  recordChoiceOfAccountClosure
} from '@/service/service';

export default {
  name: 'xhResultV2',
  inject: ['tkFlowInfo', 'eventMessage'],
  props: {
    resultDesc: {
      type: String,
      default: ''
    },
    needBackApp: {
      type: Boolean,
      default: false
    },
    isNeedVisit: {
      type: Boolean,
      default: false
    },
    subjectNo: {
      // 试卷编号
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      bizType: '',
      bizName: '',
      flowName: '',
      formId: '',
      BANDLE_STATE: BANDLE_STATE,
      TASK_TYPE,
      TASK_STATUS,
      dataResult: () => {},
      nowDate: '',
      showVisit: false,
      unifiedAccountList: [], // 一码通账户列表
      fundAccountList: [], //资金账户列表
      stockholderList: [], //证券账户列表
      creditAccList: [], //信用账户列表
      optionAccList: [], //期权账户列表
      financialAccList: [], //理财账户列表
      unifiedAccOn: true,
      fundAccOn: true,
      stockholderOn: true,
      creditAccOn: true,
      optionAccOn: true,
      financialAccOn: true,
      showDetail: false,
      tips: this.$attrs.tips,
      // operatorResult  0 处理中  1:销户完成  2:销户失败 3 撤指定完成 4转托管完成 5 撤指定失败 6转托管失败
      operatorResultList: [
        {
          key: 0,
          label: '处理中'
        },
        {
          key: 1,
          label: '销户完成'
        },
        {
          key: 2,
          label: '销户失败'
        },
        {
          key: 3,
          label: '撤挂(含撤指定)完成'
        },
        {
          key: 4,
          label: '转托管完成'
        },
        {
          key: 5,
          label: '撤挂(含撤指定)失败'
        },
        {
          key: 6,
          label: '转托管失败'
        }
      ],
      // 1  注销  2 注销  3 撤指定  4 撤挂  5 中登注销  6 转托管  7 取消登记  8 注销  9 注销  A 注销  Z 注销
      actionTypeList: [
        {
          key: 1,
          label: '注销'
        },
        {
          key: 2,
          label: '注销'
        },
        {
          key: 3,
          label: '撤挂(含撤指定)'
        },
        {
          key: 4,
          label: '撤挂'
        },
        {
          key: 5,
          label: '中登注销'
        },
        {
          key: 6,
          label: '转托管'
        },
        {
          key: 7,
          label: '取消登记'
        },
        {
          key: 8,
          label: '注销'
        },
        {
          key: 9,
          label: '注销'
        },
        {
          key: 'A',
          label: '注销'
        },
        {
          key: 'Z',
          label: '注销'
        }
      ]
    };
  },
  computed: {
    isApp() {
      return $hvue.platform !== '0';
    },
    resultClass() {
      if (this.dataResult.bandleState === BANDLE_STATE.SUCCESS) {
        return 'ok';
      } else if (this.dataResult.bandleState === BANDLE_STATE.FAIL) {
        return 'fail';
      } else {
        return 'ing';
      }
    },
    resultTips() {
      if (this.dataResult.bandleState === BANDLE_STATE.SUCCESS) {
        return '办理成功';
      } else if (this.dataResult.bandleState === BANDLE_STATE.FAIL) {
        return '办理失败';
      } else {
        return '办理中';
      }
    }
  },
  created() {
    $h.setSession('onResultPage', true);

    this.eventMessage(this, EVENT_NAME.BACK_BTN, {
      event: () => {
        this.back();
      }
    });
  },
  mounted() {
    this.renderingView();
  },
  methods: {
    toQuestion() {
      this.$router.replace({
        name: 'questionVisitDetail',
        query: {
          subjectNo: this.subjectNo,
          biz: this.bizType,
          formId: this.formId
        }
      });
    },
    preStep() {
      this.eventMessage(this, EVENT_NAME.PREV_FLOW);
    },
    back() {
      if (this.isApp && this.needBackApp) {
        this.$store.commit('user/setUserInfo', null);
        localStorage.removeItem('vuex');
        sessionStorage.clear();
        // wakeLoginApp();
        $h.callMessageNative({
          funcNo: '50114',
          moduleName: $hvue.customConfig.moduleName
        });
      } else {
        this.eventMessage(this, EVENT_NAME.TO_INDEX);
      }
    },

    renderingView() {
      const flowToken = sessionStorage.getItem('TKFlowToken');
      flowQueryIns({ flowToken })
        .then((res) => {
          this.bizType = res.data.bizType;
          this.bizName = res.data.bizName;
          this.flowName = res.data.flowName;
          this.formId = res.data.formId;
          // 进入页面查询状态，只有当前流程状态为受理中的情况，进入业务，
          // 直接提交给柜台的调用flowSubmit，跑批同步提交调用流程下一步
          // 0为受理中状态
          if (res.data.status === '0') {
            // 跑批同步提交
            this.eventMessage(this, EVENT_NAME.NEXT_STEP);
            window.tkFlowNextCallback = (data) => {
              if (data.code === 0) {
                this.showDetail = true;
                const preFlowInsId = this.$attrs.preFlowInsId;
                const flowToken = sessionStorage.getItem('TKFlowToken');
                // queryType查询类型：0 首次（默认）；1 再次；
                this.recordChoiceOfAccountClosure({
                  queryType: 0,
                  flowToken,
                  preFlowInsId
                });
              } else {
                this.showDetail = false;
                _hvueAlert({
                  mes: '提交失败，请稍后重试',
                  callback: () => {
                    $h.callMessageNative({
                      funcNo: '50114',
                      moduleName: $hvue.customConfig.moduleName
                    });
                  }
                });
              }
            };
          } else {
            this.showDetail = true;
            // 非受理中状态进入结果页
            const preFlowInsId = this.$attrs.preFlowInsId;
            const flowToken = sessionStorage.getItem('TKFlowToken');
            // queryType查询类型：0 首次（默认）；1 再次；
            this.recordChoiceOfAccountClosure({
              queryType: 1,
              flowToken,
              preFlowInsId
            });
          }
        })
        .catch((err) => {
          this.showDetail = false;
          _hvueAlert({
            mes: '请求失败，请稍后重试',
            callback: () => {
              $h.callMessageNative({
                funcNo: '50114',
                moduleName: $hvue.customConfig.moduleName
              });
            }
          });
        });
    },
    recordChoiceOfAccountClosure(reqParams) {
      const preFlowInsId = this.$attrs.preFlowInsId;
      const flowToken = sessionStorage.getItem('TKFlowToken');
      let { branchNo, clientId } = this.$store.state.user?.userInfo;
      const { bizType } = this.tkFlowInfo().inProperty;
      recordChoiceOfAccountClosure({
        clientId,
        branchNo,
        bizType,
        flowToken,
        preFlowInsId
      })
        .then(({ data }) => {
          // 账号类别：1 资金账户；2证券账户；3信用账户；4 期权账户；5 理财账户；
          for (const {
            type,
            bankAccountName = '',
            bankName = '',
            ...item
          } of data.cancelAccountDataList) {
            // operatorResult  0 处理中  1:销户完成  2:销户失败 3 撤指定完成 4转托管完成 5 撤指定失败 6转托管失败
            const statusDesc =
              this.operatorResultList.find((t) => t.key == item.operatorResult)
                ?.label ?? item.operatorResult;
            if (type === '0') {
              this.unifiedAccountList.push({ ...item, statusDesc });
            } else if (type === '1') {
              this.fundAccountList.push({ ...item, statusDesc });
              if (bankAccountName !== '') {
                this.fundAccountList.push({
                  bankAccountName,
                  bankName,
                  ...item,
                  statusDesc
                });
              }
            } else if (type === '2') {
              this.stockholderList.push({ ...item, statusDesc });
            } else if (type === '3') {
              this.creditAccList.push({ ...item, statusDesc });
            } else if (type === '4') {
              this.optionAccList.push({ ...item, statusDesc });
            } else if (type === '5') {
              this.financialAccList.push({ ...item, statusDesc });
            }
          }
        })
        .catch((err) => {
          _hvueAlert({
            mes: err
          });
        });
    },

    commentStatus({ actionType = '' }) {
      const arr = actionType.includes(',')
        ? actionType.split(',')
        : [actionType];
      const statusArr = [];
      for (let i = 0; i < arr.length; i++) {
        const tmp = this.actionTypeList.find((a) => a.key == arr[i]);
        if (tmp) {
          statusArr.push(tmp.label);
        } else {
          statusArr.push(arr[i]);
        }
      }
      return statusArr.join('，');
    }
  }
};
</script>

<style scoped>
.result_info ul li .tit {
  max-width: 2.8rem;
}

.imp_c_tips p.warn .imp {
  color: var(--impColor, #ff8533) !important;
}
</style>
