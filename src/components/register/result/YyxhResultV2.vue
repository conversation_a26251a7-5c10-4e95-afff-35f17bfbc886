<template>
  <section class="main fixed white_bg" style="position: fixed; z-index: 999">
    <t-header @back="back" />
    <article class="content" v-show="showDetail">
      <div class="result_page">
        <div class="result_tips">
          <div class="icon" :class="resultConfig.iconClass" />
          <h5>
            <span>{{ resultConfig.titlePrefix }}</span
            >{{ resultConfig.titleSuffix }}
          </h5>
          <p
            v-if="resultConfig.description"
            v-html="resultConfig.description"
          />
        </div>
        <div class="result_info">
          <ul>
            <li>
              <span class="tit">转销户预约</span>
              <p>
                <span class="state">{{ resultConfig.status }}</span>
              </p>
            </li>
          </ul>
        </div>
      </div>
    </article>
    <footer class="footer white_bg">
      <div class="ce_btn black">
        <a
          v-for="button in visibleButtons"
          :key="button.key"
          :class="['p_button', { border: button.border }]"
          @click="button.handler"
        >
          {{ button.text }}
        </a>
      </div>
    </footer>
  </section>
</template>

<script>
import {
  xhFlowInfoQryV2,
  xhDetainTaskCancelV2,
  invalidFlowIns,
  xhDetainTaskCreate,
  flowQueryIns
} from '@/service/service';
import { EVENT_NAME } from '@/common/formEnum';
import pageIdentifierBus from '@/common/tracking/pageIdentifierBus';
import { trackBusinessPageClick } from '@/common/tracking/businessTracking';

// 页面类型常量
const PAGE_TYPES = {
  RETAIN_FAILED: '0', // 挽留失败
  RETAIN_SUCCESS: '1', // 挽留成功
  NO_RETAIN_NEEDED: '2', // 无需挽留
  RETAIN_PROCESSING: '3', // 挽留处理中
  EMPTY: '' // 空状态
};

// 结果配置映射
const RESULT_CONFIGS = {
  [PAGE_TYPES.RETAIN_SUCCESS]: {
    iconClass: 'ok',
    titlePrefix: '转销户预约',
    titleSuffix: '撤销成功',
    status: '撤销成功'
  },
  [PAGE_TYPES.RETAIN_FAILED]: {
    iconClass: 'ok',
    titlePrefix: '转销户预约',
    titleSuffix: '初审通过',
    status: '预约成功',
    description:
      '您的转销户预约初审已通过，预约有效期为30天，如超过30天不做后续操作则视为自动取消销户”改为“您的预约转销户申请已审核通过，但业务尚未办理完毕，请在30天内点击“录制视频办理转销户”完成视频录制，超期将需要重新申请。'
  },
  [PAGE_TYPES.NO_RETAIN_NEEDED]: {
    iconClass: 'ok',
    titlePrefix: '转销户预约',
    titleSuffix: '初审通过',
    status: '预约成功'
  },
  [PAGE_TYPES.RETAIN_PROCESSING]: {
    iconClass: 'ing',
    titlePrefix: '成功提交，',
    titleSuffix: '处理中',
    status: '处理中',
    description:
      '我司工作人员会尽快审核您的业务申请，<span class="font_weight_bold">审核通过后，您需上传本人身份证件资料，完成正式销户视频认证。</span>请您保持手机畅通，并关注审核结果短信通知。如有疑问，请咨询您的服务人员或<span class="no-wrap">95310</span>。'
  },
  [PAGE_TYPES.EMPTY]: {
    iconClass: 'ing',
    titlePrefix: '成功提交，',
    titleSuffix: '处理中',
    status: '处理中',
    description:
      '我司工作人员会尽快审核您的业务申请，<span class="font_weight_bold">审核通过后，您需上传本人身份证件资料，完成正式销户视频认证。</span>请您保持手机畅通，并关注审核结果短信通知。如有疑问，请咨询您的服务人员或<span class="no-wrap">95310</span>。'
  }
};

export default {
  name: 'YyxhResultV2',
  inject: ['tkFlowInfo', 'eventMessage'],
  data() {
    return {
      showDetail: false,
      flowInsId: '',
      pageType: PAGE_TYPES.RETAIN_FAILED
    };
  },
  computed: {
    resultConfig() {
      return RESULT_CONFIGS[this.pageType] || RESULT_CONFIGS[PAGE_TYPES.EMPTY];
    },
    // 根据页面类型显示对应的按钮
    visibleButtons() {
      const buttons = [];
      const { pageType } = this;

      // 预约成功状态的按钮
      if (
        [PAGE_TYPES.RETAIN_FAILED, PAGE_TYPES.NO_RETAIN_NEEDED].includes(
          pageType
        )
      ) {
        buttons.push(
          {
            key: 'next',
            text: '录制视频办理转销户',
            handler: this.nextStep,
            border: false
          },
          {
            key: 'cancel',
            text: '我不销户了',
            handler: this.popupTaskCannel,
            border: true
          }
        );
      }

      // 撤销成功状态的按钮
      if (pageType === PAGE_TYPES.RETAIN_SUCCESS) {
        buttons.push({
          key: 'return',
          text: '返回',
          handler: this.flowEnd,
          border: true
        });
      }

      // 处理中状态的按钮
      if ([PAGE_TYPES.EMPTY, PAGE_TYPES.RETAIN_PROCESSING].includes(pageType)) {
        buttons.push(
          {
            key: 'cancel',
            text: '我不销户了',
            handler: this.popupTaskCannel,
            border: false
          },
          {
            key: 'back',
            text: '返回',
            handler: this.back,
            border: true
          }
        );
      }

      return buttons;
    }
  },
  mounted() {
    this.initializeView();
  },
  methods: {
    // 初始化视图数据
    async initializeView() {
      const flowToken = sessionStorage.getItem('TKFlowToken');
      if (!flowToken) {
        this.showDetail = false;
        return;
      }

      try {
        // 并行获取流程实例ID和流程信息
        const [flowRes, flowInfoRes] = await Promise.all([
          flowQueryIns({ flowToken }),
          xhFlowInfoQryV2({ flowToken })
        ]);

        this.flowInsId = flowRes.data.id;
        const { conclusion = '' } = flowInfoRes.data;
        this.pageType = conclusion;

        // 如果没有挽留状态，创建预约销户单
        if (conclusion === '') {
          await xhDetainTaskCreate({ flowToken });
        }

        if (
          [PAGE_TYPES.RETAIN_FAILED, PAGE_TYPES.NO_RETAIN_NEEDED].includes(
            this.pageType
          )
        ) {
          pageIdentifierBus.setPageIdentifier('xhyytjwlsb');
        } else if (this.pageType === PAGE_TYPES.RETAIN_SUCCESS) {
          pageIdentifierBus.setPageIdentifier('xhyytjwlcg');
        } else {
          pageIdentifierBus.setPageIdentifier('xhyytjclz');
        }

        this.showDetail = true;
      } catch (error) {
        console.error('初始化视图失败:', error);
        this.showDetail = false;
      }
    },
    // 结束流程
    async flowEnd() {
      try {
        const { code, msg } = await invalidFlowIns({
          flowInsId: this.flowInsId
        });
        if (code === 0) {
          this.back();
        } else {
          throw new Error(msg);
        }
      } catch (error) {
        this.showAlert(error.message || error);
      }
    },
    // 弹出取消确认框
    popupTaskCannel() {
      trackBusinessPageClick({
        bizType: $h.getSession('bizType'),
        pageIdentifier: 'xhyytjclz',
        elementName: 'wbxhl'
      });
      this.$TAlert({
        title: '提示',
        tips: '点击“确认”，即终止本次销户业务办理，如果您有任何意见或建议，欢迎致电95310向我们反馈。',
        hasCancel: true,
        confirmBtn: '确认',
        cancelBtn: '取消',
        confirm: this.taskCannel
      });
    },
    // 取消任务
    async taskCannel() {
      try {
        const { code, msg } = await xhDetainTaskCancelV2({
          flowToken: sessionStorage.getItem('TKFlowToken')
        });

        if (code === 0) {
          this.back();
        } else {
          throw new Error(msg);
        }
      } catch (error) {
        this.showAlert(error.message || error);
      }
    },

    // 统一的错误提示方法
    showAlert(message) {
      this.$TAlert({ tips: message });
    },
    // 返回首页
    back() {
      this.eventMessage(this, EVENT_NAME.TO_INDEX);
    },

    // 下一步
    nextStep() {
      this.eventMessage(this, EVENT_NAME.NEXT_STEP);
    }
  }
};
</script>
<style scoped>
.font_weight_bold {
  font-weight: 600;
  color: #000000;
}
</style>
